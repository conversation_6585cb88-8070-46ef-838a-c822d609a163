using UnityEditor;
using UnityEngine;
using UnityEngine.UIElements;
using System.Collections.Generic;
using System.IO;
using System.Reflection;

public class DialogueViewer : EditorWindow
{
    ViewDialogueTree treeView; // 对话节点视图实例的公共属性
    ViewDialogueInspector inspectorView; // 对话节点视图实例的公共属性
    Label dialogueTreeIDLabel;

    private Dictionary<string, string> textDictionary; // 文本字典，用于验证textID

    [MenuItem("工具/DialogueViewer _#%E")] // #=shift, &=alt, %=ctrl
    public static void ShowExample()
    {
        DialogueViewer wnd = GetWindow<DialogueViewer>(); // 创建一个对话节点编辑器窗口实例
        wnd.titleContent = new GUIContent("DialogueViewer"); // 设置窗口标题

        // 获取当前选中的 DialogueTreeSO 实例
        DialogueTreeSO tree = Selection.activeObject as DialogueTreeSO;
        if (tree)
        {
            // 调用 OnSelectionChange 方法，立即显示当前选中的实例内容
            wnd.OnSelectionChange();
        }
    }

    [UnityEditor.Callbacks.OnOpenAsset(1)]
    public static bool OnOpenAsset(int instanceID, int line)
    {
        // 获取被双击的资产
        UnityEngine.Object obj = EditorUtility.InstanceIDToObject(instanceID);

        // 检查资产是否为 DialogueTreeSO 类型
        if (obj is DialogueTreeSO)
        {
            // 打开 DialogueViewer 窗口
            ShowExample();
            return true;
        }
        return false;
    }

    private void OnEnable()
    {
        // 窗口启用时执行验证（延迟执行，确保GUI已初始化）
        EditorApplication.delayCall += () =>
        {
            if (this != null && treeView?.tree != null)
            {
                ValidateAllTextIDs();
            }
        };
    }

    private void OnFocus()
    {
        // 窗口获得焦点时执行验证
        if (treeView?.tree != null)
        {
            ValidateAllTextIDs();
        }
    }

    public void CreateGUI()
    {
        VisualElement root = rootVisualElement; // 获取根VisualElement

        var VisualTree = AssetDatabase.LoadAssetAtPath<VisualTreeAsset>("Assets/Scripts/DialogueTree/Editor/DialogueViewer.uxml");
        VisualTree.CloneTree(root); // 克隆VisualTreeAsset中的元素到根VisualElement中

        var stylesheet = AssetDatabase.LoadAssetAtPath<StyleSheet>("Assets/Scripts/DialogueTree/Editor/DialogueViewer.uss");
        root.styleSheets.Add(stylesheet); // 将USS文件添加到根VisualElement的样式表中

        treeView = root.Q<ViewDialogueTree>(); // 获取对话节点视图实例
        inspectorView = root.Q<ViewDialogueInspector>(); // 获取对话节点视图实例

        dialogueTreeIDLabel = root.Q<Label>("DialogueTreeID"); // 获取 Label 组件

        Button refreshViewButton = root.Q<Button>("RefreshView"); // 获取刷新视图按钮
        refreshViewButton.clicked += () => EditorDialogueFeatures.RefreshView(treeView, inspectorView, dialogueTreeIDLabel);

        Button verticalSortingButton = root.Q<Button>("VerticalSorting"); // 获取排列按钮
        verticalSortingButton.clicked += () => EditorDialogueFeatures.VerticalSort(treeView);

        // Button MiniMapButton = root.Q<Button>("MiniMap"); // 获取地图按钮
        // MiniMapButton.clicked += () => treeView.ToggleMiniMap(); // 添加点击事件处理，切换MiniMap的显示/隐藏

        treeView.OnNodeSelected = OnNodeSelectionChaned; // 设置节点选择回调函数

        // 初始化文本字典
        InitializeTextDictionary();
    }

    private void OnNodeSelectionChaned(ViewDialogueNode view) // 当节点被选择时
    {
        inspectorView.UpdateSelection(view); // 填充节点视图
    }

    private void OnSelectionChange()
    {
        DialogueTreeSO tree = Selection.activeObject as DialogueTreeSO;
        if (tree)
        {
            treeView.PopulateView(tree);
            treeView.EnsureRootNodeExists(tree); // 确保根节点存在

            dialogueTreeIDLabel.text = tree.name; // 更新 Label 组件的文本

            // 选择新对话树时执行文本ID验证
            ValidateAllTextIDs();
        }
    }

    /// <summary>
    /// 初始化文本字典，从TSV文件加载文本数据
    /// </summary>
    private void InitializeTextDictionary()
    {
        textDictionary = new Dictionary<string, string>();
        string filePath = Path.Combine(Application.streamingAssetsPath, "language/简体中文.tsv");
        if (File.Exists(filePath))
        {
            string[] lines = File.ReadAllLines(filePath);
            foreach (string line in lines)
            {
                string[] parts = line.Split('\t');
                if (parts.Length >= 4)
                {
                    textDictionary[parts[1]] = parts[3]; // parts[1]是textID，parts[3]是content
                }
            }
        }
    }

    /// <summary>
    /// 验证当前对话树中所有节点的文本ID，如果无效则清空textContent
    /// </summary>
    private void ValidateAllTextIDs()
    {
        if (treeView?.tree == null || textDictionary == null) return;

        DialogueTreeSO tree = treeView.tree;
        bool hasChanges = false;

        foreach (DialogueNodeSO node in tree.allNodes)
        {
            if (ValidateNodeTextIDs(node))
            {
                hasChanges = true;
            }
        }

        if (hasChanges)
        {
            EditorUtility.SetDirty(tree);
            AssetDatabase.SaveAssets();
            Debug.Log($"已验证对话树 '{tree.name}' 中的文本ID，无效的textContent已被清空");
        }
    }

    /// <summary>
    /// 验证单个节点的文本ID
    /// </summary>
    /// <param name="node">要验证的节点</param>
    /// <returns>是否有修改</returns>
    private bool ValidateNodeTextIDs(DialogueNodeSO node)
    {
        bool hasChanges = false;

        // 使用反射获取dialogueList字段
        FieldInfo dialogueListField = node.GetType().GetField("dialogueList");
        if (dialogueListField != null)
        {
            if (dialogueListField.GetValue(node) is System.Array dialogueList)
            {
                for (int i = 0; i < dialogueList.Length; i++)
                {
                    var dialogue = dialogueList.GetValue(i);
                    if (dialogue != null)
                    {
                        // 获取textID和textContent字段
                        FieldInfo textIDField = dialogue.GetType().GetField("textID");
                        FieldInfo textContentField = dialogue.GetType().GetField("textContent");

                        if (textIDField != null && textContentField != null)
                        {
                            string textID = textIDField.GetValue(dialogue) as string;
                            string currentTextContent = textContentField.GetValue(dialogue) as string;

                            // 如果textID无效且textContent不为空，则清空textContent
                            if (!string.IsNullOrEmpty(textID) &&
                                !textDictionary.ContainsKey(textID) &&
                                !string.IsNullOrEmpty(currentTextContent))
                            {
                                textContentField.SetValue(dialogue, "");
                                hasChanges = true;
                                Debug.Log($"节点 '{node.title}' 中的无效textID '{textID}' 对应的textContent已被清空");
                            }
                        }
                    }
                }
            }
        }

        return hasChanges;
    }
}